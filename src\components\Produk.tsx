import React, { useState, useEffect } from 'react';
import './Produk.css';
import UniversalHeader from './UniversalHeader';
import HomeButton from './HomeButton';

/**
 * Interface untuk data produk dari <PERSON> API
 */
interface Product {
  id: number;
  name: string;
  category: string;
  image: string;
  description: string;
  price: number | null;
  merek: string | null;
  unit_model: string | null;
  warranty: string | null;
  discount: string | null;
  notes: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

/**
 * Interface untuk API response
 */
interface ApiResponse {
  success: boolean;
  data: Product[];
}

/**
 * Props untuk komponen Produk
 */
interface ProdukProps {
  className?: string;
}

/**
 * Komponen Produk dengan layout grid responsif
 * Menampilkan daftar produk dari <PERSON> API
 */
const Produk: React.FC<ProdukProps> = ({ className = '' }) => {
  const [selectedProduct, setSelectedProduct] = useState<number | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // API base URL - using 127.0.0.1 to match Laravel server
  const API_BASE_URL = 'http://127.0.0.1:8000/api/v1';

  // Fetch products from Laravel API
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log('Fetching products from:', `${API_BASE_URL}/products`);

        const response = await fetch(`${API_BASE_URL}/products`, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
          mode: 'cors',
        });

        console.log('Response status:', response.status);
        console.log('Response ok:', response.ok);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Response error text:', errorText);
          throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
        }

        const data: ApiResponse = await response.json();
        console.log('API Response:', data);

        if (data.success && Array.isArray(data.data)) {
          setProducts(data.data);
          console.log('Products loaded successfully:', data.data.length, 'items');
        } else {
          throw new Error('API returned unsuccessful response or invalid data format');
        }
      } catch (err) {
        console.error('Error fetching products:', err);
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch products';

        // Try to use fallback data if API fails
        console.log('API failed, attempting to use fallback data...');
        const fallbackProducts: Product[] = [
          {
            id: 1,
            name: 'COMPRESSOR',
            category: 'AC Parts',
            image: '/images/product/img1.png',
            description: 'Kompressor AC berkualitas tinggi untuk berbagai jenis kendaraan',
            price: 6000000,
            merek: 'SANNY',
            unit_model: 'SY500',
            warranty: '3 Bulan',
            discount: null,
            notes: 'Warranty 3 Bulan',
            is_active: true,
            created_at: '2025-08-12T14:04:00.000000Z',
            updated_at: '2025-08-12T14:04:00.000000Z',
          },
          {
            id: 2,
            name: 'EVAPORATOR',
            category: 'AC Parts',
            image: '/images/product/img2.png',
            description: 'Evaporator AC dengan teknologi terbaru untuk pendinginan optimal',
            price: 2500000,
            merek: 'DENSO',
            unit_model: 'EV-200',
            warranty: '2 Tahun',
            discount: null,
            notes: null,
            is_active: true,
            created_at: '2025-08-12T14:04:00.000000Z',
            updated_at: '2025-08-12T14:04:00.000000Z',
          },
          {
            id: 3,
            name: 'CONDENSER',
            category: 'AC Parts',
            image: '/images/product/img1.png',
            description: 'Kondenser AC dengan material berkualitas untuk transfer panas optimal',
            price: 3200000,
            merek: 'DENSO',
            unit_model: 'CD-500',
            warranty: '2 Tahun',
            discount: null,
            notes: null,
            is_active: true,
            created_at: '2025-08-12T14:04:00.000000Z',
            updated_at: '2025-08-12T14:04:00.000000Z',
          },
        ];

        setProducts(fallbackProducts);
        setError(`Menggunakan data offline: ${errorMessage}`);
        console.log('Fallback data loaded:', fallbackProducts.length, 'items');
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [API_BASE_URL]);

  /**
   * Fungsi untuk menangani klik tombol "Lihat Detail"
   * @param productId - ID produk yang dipilih
   */
  const handleViewProductDetail = (productId: number) => {
    setSelectedProduct(selectedProduct === productId ? null : productId);
  };

  /**
   * Fungsi untuk mendapatkan produk yang dipilih
   */
  const getSelectedProduct = () => {
    return products.find(product => product.id === selectedProduct);
  };

  /**
   * Format harga dalam Rupiah
   */
  const formatPrice = (price: number | null) => {
    if (!price) return 'Harga tidak tersedia';
    return `Rp ${price.toLocaleString('id-ID')}`;
  };

  // Loading state
  if (loading) {
    return (
      <div className={`produk-container h-screen max-h-screen overflow-hidden bg-base-100 flex flex-col ${className}`}>
        <UniversalHeader />
        <div className="container mx-auto p-4 flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="loading loading-spinner loading-lg text-primary"></div>
            <p className="mt-4 text-base-content">Memuat produk...</p>
          </div>
        </div>
        <HomeButton />
      </div>
    );
  }

  // Error state - only show error UI if no products are available
  if (error && products.length === 0) {
    return (
      <div className={`produk-container h-screen max-h-screen overflow-hidden bg-base-100 flex flex-col ${className}`}>
        <UniversalHeader />
        <div className="container mx-auto p-4 flex-1 flex items-center justify-center">
          <div className="text-center max-w-md">
            <div className="alert alert-error">
              <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div>
                <div className="font-bold">Koneksi API Bermasalah</div>
                <div className="text-sm mt-1">{error}</div>
              </div>
            </div>
            <div className="mt-4 text-sm text-base-content opacity-70">
              <p>API URL: {API_BASE_URL}/products</p>
              <p>Pastikan Laravel server berjalan di port 8000</p>
            </div>
            <div className="mt-4 space-x-2">
              <button
                className="btn btn-primary btn-sm"
                onClick={() => window.location.reload()}
              >
                Coba Lagi
              </button>
              <button
                className="btn btn-outline btn-sm"
                onClick={() => window.open(`${API_BASE_URL}/products`, '_blank')}
              >
                Test API
              </button>
            </div>
          </div>
        </div>
        <HomeButton />
      </div>
    );
  }

  return (
    <div className={`produk-container h-screen max-h-screen overflow-hidden bg-base-100 flex flex-col ${className}`}>
      {/* Universal Header */}
      <UniversalHeader />

      {/* Offline Mode Banner */}
      {error && products.length > 0 && (
        <div className="bg-warning text-warning-content px-4 py-2 text-sm flex items-center justify-between">
          <div className="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span>Mode Offline - Menampilkan data tersimpan</span>
          </div>
          <button
            className="btn btn-xs btn-ghost"
            onClick={() => window.location.reload()}
          >
            Coba Lagi
          </button>
        </div>
      )}

      {/* Main Content - Flexible height */}
      <div className="container mx-auto p-4 flex-1 overflow-hidden">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-full">
          {/* Grid Produk */}
          <div className="lg:col-span-2 overflow-y-auto">
            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-3 gap-2">
              {products.map((product) => (
                <div
                  key={product.id}
                  className={`product-card card bg-base-100 hover:shadow-xl transition-all duration-300 ${
                    selectedProduct === product.id ? 'ring-2 ring-primary' : ''
                  }`}
                >
                  {/* Gambar Produk */}
                  <figure className="px-2 pt-2">
                    <img src={product.image} alt={product.name} className="rounded-lg w-full h-20 object-cover" loading="lazy" />
                  </figure>

                  {/* Konten Card */}
                  <div className="card-body p-4">
                    <h2 className="card-title text-lg font-bold text-primary">
                      {product.name}
                    </h2>
                    <p className="text-xs text-base-content opacity-70 mb-2">
                      {product.description}
                    </p>
                    <div className="badge badge-secondary badge-sm mb-3">
                      {product.category}
                    </div>

                    {/* Harga */}
                    {product.price && (
                      <div className="text-sm font-semibold text-primary mb-2">
                        {formatPrice(product.price)}
                      </div>
                    )}

                    {/* Tombol Lihat Detail */}
                    <div className="card-actions justify-end">
                      <button
                        className={`btn btn-primary btn-sm ${
                          selectedProduct === product.id ? 'btn-active' : ''
                        }`}
                        onClick={() => handleViewProductDetail(product.id)}
                      >
                        {selectedProduct === product.id ? 'Tutup Detail' : 'Lihat Detail'}
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Detail Produk */}
          <div className="lg:col-span-1 overflow-hidden">
            <div className={`product-detail-container h-full transition-all duration-500 ${
              selectedProduct ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4 pointer-events-none'
            }`}>
              {selectedProduct && getSelectedProduct() && (
                <div className="card bg-base-100 shadow-lg h-full flex flex-col">
                  <div className="card-body p-4 flex-1 overflow-hidden flex flex-col">
                    {/* Header Detail */}
                    <div className="flex items-center gap-3 mb-4 flex-shrink-0">
                      <img
                        src={getSelectedProduct()!.image}
                        alt={getSelectedProduct()!.name}
                        className="w-12 h-12 rounded-lg object-cover"
                      />
                      <div>
                        <h3 className="text-lg font-bold text-primary">
                          {getSelectedProduct()!.name}
                        </h3>
                        <div className="badge badge-secondary badge-sm">
                          {getSelectedProduct()!.category}
                        </div>
                      </div>
                    </div>

                    {/* Detail Content */}
                    <div className="flex-1 overflow-y-auto">
                      <div className="space-y-3">
                        <div>
                          <h4 className="font-semibold text-sm mb-1">Deskripsi:</h4>
                          <p className="text-xs text-base-content opacity-80">
                            {getSelectedProduct()!.description}
                          </p>
                        </div>

                        {getSelectedProduct()!.price && (
                          <div>
                            <h4 className="font-semibold text-sm mb-1">Harga:</h4>
                            <p className="text-lg font-bold text-primary">
                              {formatPrice(getSelectedProduct()!.price)}
                            </p>
                            {getSelectedProduct()!.discount && (
                              <p className="text-xs text-success">
                                {getSelectedProduct()!.discount}
                              </p>
                            )}
                          </div>
                        )}

                        {getSelectedProduct()!.merek && (
                          <div>
                            <h4 className="font-semibold text-sm mb-1">Merek:</h4>
                            <p className="text-sm">{getSelectedProduct()!.merek}</p>
                          </div>
                        )}

                        {getSelectedProduct()!.unit_model && (
                          <div>
                            <h4 className="font-semibold text-sm mb-1">Model:</h4>
                            <p className="text-sm">{getSelectedProduct()!.unit_model}</p>
                          </div>
                        )}

                        {getSelectedProduct()!.warranty && (
                          <div>
                            <h4 className="font-semibold text-sm mb-1">Garansi:</h4>
                            <div className="badge badge-info badge-sm">
                              {getSelectedProduct()!.warranty}
                            </div>
                          </div>
                        )}

                        {getSelectedProduct()!.notes && (
                          <div>
                            <h4 className="font-semibold text-sm mb-1">Catatan:</h4>
                            <p className="text-xs text-base-content opacity-80">
                              {getSelectedProduct()!.notes}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Home Button */}
      <HomeButton />
    </div>
  );
};

export default Produk;
